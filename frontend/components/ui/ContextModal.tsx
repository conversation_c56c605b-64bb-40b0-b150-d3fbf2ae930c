'use client'

import { useState, useEffect } from 'react'
import { X, ChevronLeft, ChevronRight, Quote, Building, Hash, Users } from 'lucide-react'
import { Button } from './Button'
import { Badge } from './Badge'

interface ContextItem {
  stock_code: string
  company_name: string
  related_party?: string | null
  context: string
  keywords_found: string[]
  has_keywords: boolean
  context_type: string
}

interface ContextModalProps {
  isOpen: boolean
  onClose: () => void
  contexts: ContextItem[]
}

export function ContextModal({ isOpen, onClose, contexts }: ContextModalProps) {
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 5

  // 重置页码当模态框打开时
  useEffect(() => {
    if (isOpen) {
      setCurrentPage(1)
    }
  }, [isOpen])

  if (!isOpen) return null

  // 验证contexts数据
  if (!Array.isArray(contexts)) {
    console.error('❌ ContextModal: contexts不是数组', contexts)
    return null
  }

  console.log('🔍 ContextModal渲染 - 上下文数量:', contexts.length)

  const totalPages = Math.ceil(contexts.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentContexts = contexts.slice(startIndex, endIndex)

  const getContextTypeLabel = (type: string) => {
    switch (type) {
      case 'with_keywords':
        return '关键词+关联方'
      case 'related_party_only':
        return '仅关联方'
      case 'keyword_only':
        return '仅关键词'
      default:
        return '未知类型'
    }
  }

  const getContextTypeBadge = (type: string) => {
    switch (type) {
      case 'with_keywords':
        return <Badge variant="success" size="sm">关键词+关联方</Badge>
      case 'related_party_only':
        return <Badge variant="warning" size="sm">仅关联方</Badge>
      case 'keyword_only':
        return <Badge variant="info" size="sm">仅关键词</Badge>
      default:
        return <Badge variant="outline" size="sm">未知</Badge>
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] flex flex-col">
        {/* 标题栏 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <Quote className="w-6 h-6 text-blue-600" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">引用上下文</h2>
              <p className="text-sm text-gray-500 mt-1">
                共找到 {contexts.length} 个相关上下文片段
              </p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="p-2"
          >
            <X className="w-5 h-5" />
          </Button>
        </div>

        {/* 内容区域 */}
        <div className="flex-1 overflow-y-auto p-6">
          {contexts.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <Quote className="w-12 h-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                暂无引用上下文
              </h3>
              <p className="text-gray-500">
                当前分析没有找到相关的上下文信息
              </p>
            </div>
          ) : (
            <div className="space-y-6">
              {currentContexts.map((context, index) => (
              <div key={startIndex + index} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                {/* 上下文头部信息 */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <Badge variant="outline" size="sm">
                        #{startIndex + index + 1}
                      </Badge>
                      {getContextTypeBadge(context.context_type)}
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2 text-sm">
                        <Building className="w-4 h-4 text-gray-500" />
                        <span className="font-medium text-gray-900">
                          {context.company_name}
                        </span>
                        <span className="text-gray-500">({context.stock_code})</span>
                      </div>
                      
                      {context.related_party && (
                        <div className="flex items-center space-x-2 text-sm">
                          <Users className="w-4 h-4 text-gray-500" />
                          <span className="text-gray-700">关联方: {context.related_party}</span>
                        </div>
                      )}
                      
                      {context.keywords_found.length > 0 && (
                        <div className="flex items-start space-x-2 text-sm">
                          <Hash className="w-4 h-4 text-gray-500 mt-0.5" />
                          <div className="flex flex-wrap gap-1">
                            <span className="text-gray-700 mr-2">发现关键词:</span>
                            {context.keywords_found.map((keyword, idx) => (
                              <Badge key={idx} variant="secondary" size="sm">
                                {keyword}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* 上下文内容 */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">上下文内容:</h4>
                  <div className="text-sm text-gray-800 leading-relaxed whitespace-pre-wrap">
                    {context.context}
                  </div>
                </div>
              </div>
              ))}
            </div>
          )}
        </div>

        {/* 分页控制 */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between p-6 border-t border-gray-200">
            <div className="text-sm text-gray-600">
              显示第 {startIndex + 1} - {Math.min(endIndex, contexts.length)} 条，
              共 {contexts.length} 条上下文
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="w-4 h-4 mr-1" />
                上一页
              </Button>
              
              <div className="flex items-center space-x-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  const page = i + 1
                  return (
                    <Button
                      key={page}
                      variant={currentPage === page ? "primary" : "ghost"}
                      size="sm"
                      onClick={() => setCurrentPage(page)}
                      className="w-8 h-8 p-0"
                    >
                      {page}
                    </Button>
                  )
                })}
                {totalPages > 5 && (
                  <>
                    <span className="text-gray-500">...</span>
                    <Button
                      variant={currentPage === totalPages ? "primary" : "ghost"}
                      size="sm"
                      onClick={() => setCurrentPage(totalPages)}
                      className="w-8 h-8 p-0"
                    >
                      {totalPages}
                    </Button>
                  </>
                )}
              </div>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
              >
                下一页
                <ChevronRight className="w-4 h-4 ml-1" />
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
