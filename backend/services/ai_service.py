"""
AI服务模块
"""
import os
import json
from typing import List, Dict, Optional, Any, Generator
from models.database import DatabaseManager
from config import get_config

# 检查依赖
try:
    import openai
    HAS_OPENAI = True
except ImportError:
    HAS_OPENAI = False


class AIService:
    """AI服务类"""

    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        self.has_openai = HAS_OPENAI
        self.config = get_config()

        # 从配置文件获取默认设置
        self.default_api_key = self.config.OPENAI_API_KEY
        self.default_base_url = self.config.OPENAI_BASE_URL
        self.default_model = self.config.OPENAI_MODEL
        self.context_length = self.config.CONTEXT_LENGTH
        self.max_contexts = self.config.MAX_CONTEXTS
        self.analysis_timeout = self.config.AI_ANALYSIS_TIMEOUT

        # 打印OpenAI库版本信息
        if self.has_openai:
            try:
                import openai
                version = getattr(openai, '__version__', 'unknown')
                has_new_api = hasattr(openai, 'OpenAI')
                print(f"🔧 OpenAI库版本: {version}, 新API支持: {has_new_api}")
            except Exception as e:
                print(f"⚠️ 无法获取OpenAI库信息: {e}")
    
    def test_openai_connection(self, api_key: str = None, base_url: str = None, model: str = None) -> Dict[str, Any]:
        """测试OpenAI连接"""
        if not self.has_openai:
            return {
                'success': False,
                'error': 'openai库未安装，请先安装：pip install openai'
            }

        # 使用传入的参数或默认配置
        api_key = api_key or self.default_api_key
        base_url = base_url or self.default_base_url
        model = model or self.default_model
        
        try:
            # 确保base_url格式正确
            if not base_url.startswith('http'):
                base_url = 'https://' + base_url
            if not base_url.endswith('/v1'):
                if base_url.endswith('/'):
                    base_url = base_url + 'v1'
                else:
                    base_url = base_url + '/v1'

            # 检查openai库版本并使用相应的API
            if hasattr(openai, 'OpenAI'):
                # 新版本 openai >= 1.0
                client = openai.OpenAI(
                    api_key=api_key,
                    base_url=base_url
                )

                response = client.chat.completions.create(
                    model=model,
                    messages=[{"role": "user", "content": "请回复：测试成功"}],
                    max_tokens=50,
                    temperature=0
                )

                content = response.choices[0].message.content.strip()
            else:
                # 旧版本 openai < 1.0
                # 临时设置API配置
                original_api_key = getattr(openai, 'api_key', None)
                original_api_base = getattr(openai, 'api_base', None)

                openai.api_key = api_key
                openai.api_base = base_url

                response = openai.ChatCompletion.create(
                    model=model,
                    messages=[{"role": "user", "content": "请回复：测试成功"}],
                    max_tokens=50,
                    temperature=0
                )

                # 恢复原始配置
                if original_api_key:
                    openai.api_key = original_api_key
                if original_api_base:
                    openai.api_base = original_api_base

                content = response.choices[0].message.content.strip()

            return {
                'success': True,
                'response': content
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def ai_analysis(self, stock_codes: List[str], keywords: List[str], 
                   related_parties: List[str], prompt: str, 
                   openai_config: Dict = None) -> Dict[str, Any]:
        """AI分析接口"""
        print("🤖 开始AI分析...")
        
        # 获取年报数据
        print("📊 查询本地年报数据...")
        print(f"🔍 查询股票代码: {stock_codes}")
        reports = self.db.get_reports_by_stock_codes(stock_codes)
        print(f"📄 找到 {len(reports)} 个年报")

        if reports:
            for report in reports[:3]:  # 显示前3个报告的信息
                print(f"  📋 {report['stock_code']} - {report.get('company_name', 'Unknown')} - {report.get('file_name', 'Unknown')}")
                print(f"      内容长度: {len(report.get('txt_content', '')) if report.get('txt_content') else 0} 字符")

        if not reports:
            raise ValueError("没有找到相关的年报数据，请先导入txt文件或在线下载年报")
        
        # 执行上下文搜索
        contexts = []
        for report in reports:
            stock_code = report['stock_code']
            company_name = report.get('company_name', '')
            txt_content = report['txt_content']

            if not txt_content:
                continue

            # 如果有关联方，基于关联方搜索上下文
            if related_parties:
                print(f"🔍 基于关联方搜索上下文: {related_parties}")
                for related_party in related_parties:
                    party_contexts = self.find_related_party_contexts(txt_content, related_party, keywords)
                    print(f"  📍 关联方 '{related_party}' 找到 {len(party_contexts)} 个上下文")

                    for context_info in party_contexts:
                        contexts.append({
                            'stock_code': stock_code,
                            'company_name': company_name,
                            'related_party': related_party,
                            'context': context_info['text'],
                            'keywords_found': context_info['keywords_found'],
                            'has_keywords': context_info['has_keywords'],
                            'context_type': 'with_keywords' if context_info['has_keywords'] else 'related_party_only'
                        })
            else:
                # 如果没有关联方，直接基于关键词搜索上下文
                print(f"🔍 基于关键词搜索上下文: {keywords}")
                keyword_contexts = self.find_keyword_contexts(txt_content, keywords)
                print(f"  📍 关键词搜索找到 {len(keyword_contexts)} 个上下文")

                for context_info in keyword_contexts:
                    contexts.append({
                        'stock_code': stock_code,
                        'company_name': company_name,
                        'related_party': None,
                        'context': context_info['text'],
                        'keywords_found': context_info['keywords_found'],
                        'has_keywords': True,
                        'context_type': 'keyword_only'
                    })

        print(f"📊 总共找到 {len(contexts)} 个上下文")

        if not contexts:
            print("❌ 未找到任何相关上下文")
            return {
                'success': False,
                'message': '未找到相关上下文，无法进行AI分析'
            }
        
        # 生成AI分析
        if self.has_openai and openai_config:
            analysis_result = self.generate_ai_analysis(contexts, prompt, openai_config)
        else:
            analysis_result = self.generate_mock_analysis(contexts, prompt)
        
        return {
            'success': True,
            'analysis_result': analysis_result,
            'contexts_count': len(contexts),
            'message': f'AI分析完成，共分析 {len(reports)} 个年报文件'
        }
    
    def ai_analysis_stream_with_contexts(self, stock_codes: List[str], keywords: List[str],
                                        related_parties: List[str], prompt: str,
                                        contexts: List[Dict], openai_config: Dict = None) -> Generator[str, None, None]:
        """使用预获取上下文的流式AI分析"""
        try:
            print(f"🔍 开始AI分析，使用预获取的上下文: {len(contexts)} 个")

            yield f"data: {json.dumps({'type': 'status', 'message': f'使用预获取的 {len(contexts)} 个上下文，开始AI分析...'})}\n\n"

            # 直接发送预获取的上下文数据
            if contexts:
                yield f"data: {json.dumps({'type': 'contexts', 'data': contexts})}\n\n"

            # 生成AI响应（流式）
            if contexts:
                yield from self.generate_ai_response_stream(contexts, prompt, openai_config)
            else:
                yield f"data: {json.dumps({'type': 'ai_chunk', 'data': '未找到相关上下文，无法进行AI分析。'})}\n\n"

            yield f"data: {json.dumps({'type': 'complete', 'message': 'AI分析完成'})}\n\n"

        except Exception as e:
            print(f"❌ 流式AI分析失败: {e}")
            import traceback
            traceback.print_exc()
            yield f"data: {json.dumps({'type': 'error', 'message': f'分析失败: {str(e)}'})}\n\n"

    def ai_analysis_stream(self, stock_codes: List[str], keywords: List[str],
                          related_parties: List[str], prompt: str,
                          openai_config: Dict = None) -> Generator[str, None, None]:
        """流式AI分析（原版本，保持兼容性）"""
        try:
            # 获取年报数据
            reports = self.db.get_reports_by_stock_codes(stock_codes)
            if not reports:
                yield f"data: {json.dumps({'type': 'error', 'message': '没有找到相关的年报数据'})}\n\n"
                return

            yield f"data: {json.dumps({'type': 'status', 'message': f'找到 {len(reports)} 个年报'})}\n\n"

            # 执行上下文搜索
            contexts = []
            for report in reports:
                stock_code = report['stock_code']
                company_name = report.get('company_name', '')
                txt_content = report['txt_content']

                if not txt_content:
                    continue

                # 如果有关联方，基于关联方搜索上下文
                if related_parties:
                    for related_party in related_parties:
                        party_contexts = self.find_related_party_contexts(txt_content, related_party, keywords)

                        for context_info in party_contexts:
                            contexts.append({
                                'stock_code': stock_code,
                                'company_name': company_name,
                                'related_party': related_party,
                                'context': context_info['text'],
                                'keywords_found': context_info['keywords_found'],
                                'has_keywords': context_info['has_keywords'],
                                'context_type': 'with_keywords' if context_info['has_keywords'] else 'related_party_only'
                            })
                else:
                    # 如果没有关联方，直接基于关键词搜索上下文
                    keyword_contexts = self.find_keyword_contexts(txt_content, keywords)

                    for context_info in keyword_contexts:
                        contexts.append({
                            'stock_code': stock_code,
                            'company_name': company_name,
                            'related_party': None,
                            'context': context_info['text'],
                            'keywords_found': context_info['keywords_found'],
                            'has_keywords': True,
                            'context_type': 'keyword_only'
                        })

            yield f"data: {json.dumps({'type': 'contexts', 'data': contexts})}\n\n"
            yield f"data: {json.dumps({'type': 'status', 'message': f'找到 {len(contexts)} 个相关上下文，开始AI分析...'})}\n\n"

            # 生成AI响应（流式）
            if contexts:
                yield from self.generate_ai_response_stream(contexts, prompt, openai_config)
            else:
                yield f"data: {json.dumps({'type': 'ai_chunk', 'data': '未找到相关上下文，无法进行AI分析。'})}\n\n"

            yield f"data: {json.dumps({'type': 'complete', 'message': 'AI分析完成'})}\n\n"

        except Exception as e:
            print(f"❌ 流式AI分析失败: {e}")
            import traceback
            traceback.print_exc()
            yield f"data: {json.dumps({'type': 'error', 'message': f'分析失败: {str(e)}'})}\n\n"
    
    def find_related_party_contexts(self, text: str, related_party: str,
                                   keywords: List[str], context_length: int = None) -> List[Dict]:
        """查找关联方相关上下文"""
        # 使用配置中的默认值
        context_length = context_length or self.context_length

        contexts = []
        
        # 找到所有关联方出现的位置
        party_positions = []
        start = 0
        while True:
            pos = text.find(related_party, start)
            if pos == -1:
                break
            party_positions.append(pos)
            start = pos + 1
        
        # 对每个位置提取上下文
        for pos in party_positions:
            start_pos = max(0, pos - context_length)
            end_pos = min(len(text), pos + len(related_party) + context_length)
            context_text = text[start_pos:end_pos]
            
            # 检查上下文中的关键词
            keywords_found = []
            for keyword in keywords:
                if keyword in context_text:
                    keywords_found.append(keyword)
            
            contexts.append({
                'text': context_text,
                'keywords_found': keywords_found,
                'has_keywords': len(keywords_found) > 0,
                'position': pos
            })
        
        return contexts

    def find_keyword_contexts(self, text: str, keywords: List[str],
                             context_length: int = None, max_contexts: int = None) -> List[Dict]:
        """查找关键词相关上下文"""
        # 使用配置中的默认值
        context_length = context_length or self.context_length
        max_contexts = max_contexts or self.max_contexts

        contexts = []

        # 找到所有关键词出现的位置
        keyword_positions = []
        for keyword in keywords:
            start = 0
            while True:
                pos = text.find(keyword, start)
                if pos == -1:
                    break
                keyword_positions.append({
                    'keyword': keyword,
                    'position': pos
                })
                start = pos + 1

        # 按位置排序
        keyword_positions.sort(key=lambda x: x['position'])

        # 去重和合并相近的上下文
        merged_contexts = []
        for kw_pos in keyword_positions:
            pos = kw_pos['position']
            keyword = kw_pos['keyword']

            start_pos = max(0, pos - context_length)
            end_pos = min(len(text), pos + len(keyword) + context_length)

            # 检查是否与已有上下文重叠
            overlapped = False
            for existing in merged_contexts:
                if (start_pos < existing['end_pos'] and end_pos > existing['start_pos']):
                    # 合并重叠的上下文
                    existing['start_pos'] = min(existing['start_pos'], start_pos)
                    existing['end_pos'] = max(existing['end_pos'], end_pos)
                    existing['keywords_found'].append(keyword)
                    overlapped = True
                    break

            if not overlapped:
                merged_contexts.append({
                    'start_pos': start_pos,
                    'end_pos': end_pos,
                    'keywords_found': [keyword]
                })

        # 提取最终的上下文文本
        for ctx in merged_contexts[:max_contexts]:  # 限制上下文数量
            context_text = text[ctx['start_pos']:ctx['end_pos']]
            contexts.append({
                'text': context_text,
                'keywords_found': list(set(ctx['keywords_found'])),  # 去重
                'has_keywords': True,
                'position': ctx['start_pos']
            })

        return contexts

    def generate_ai_analysis(self, contexts: List[Dict], prompt: str,
                           openai_config: Dict) -> str:
        """生成AI分析（非流式）"""
        if not self.has_openai:
            return self.generate_mock_analysis(contexts, prompt)
        
        try:
            # 构建分析内容
            analysis_content = self.build_analysis_content(contexts, prompt)
            
            # 配置OpenAI，优先使用传入的配置，然后是默认配置
            api_key = openai_config.get('api_key') or self.default_api_key
            base_url = openai_config.get('base_url') or self.default_base_url
            model = openai_config.get('model') or self.default_model

            # 检查openai库版本并使用相应的API
            if hasattr(openai, 'OpenAI'):
                # 新版本 openai >= 1.0
                client = openai.OpenAI(
                    api_key=api_key,
                    base_url=base_url
                )

                response = client.chat.completions.create(
                    model=model,
                    messages=[
                        {"role": "system", "content": "你是一个专业的年报分析师，擅长分析企业协同创新情况。"},
                        {"role": "user", "content": analysis_content}
                    ],
                    max_tokens=2000,
                    temperature=0.7
                )

                return response.choices[0].message.content.strip()
            else:
                # 旧版本 openai < 1.0
                # 临时设置配置
                original_api_key = getattr(openai, 'api_key', None)
                original_api_base = getattr(openai, 'api_base', None)

                openai.api_key = api_key
                if base_url != 'https://api.openai.com/v1':
                    openai.api_base = base_url

                response = openai.ChatCompletion.create(
                    model=model,
                    messages=[
                        {"role": "system", "content": "你是一个专业的年报分析师，擅长分析企业协同创新情况。"},
                        {"role": "user", "content": analysis_content}
                    ],
                    max_tokens=2000,
                    temperature=0.7
                )

                # 恢复配置
                if original_api_key:
                    openai.api_key = original_api_key
                if original_api_base:
                    openai.api_base = original_api_base

                return response.choices[0].message.content.strip()
            
        except Exception as e:
            print(f"❌ AI分析失败: {e}")
            return f"AI分析失败: {str(e)}\n\n{self.generate_mock_analysis(contexts, prompt)}"
    
    def generate_ai_response_stream(self, contexts: List[Dict], prompt: str, 
                                   openai_config: Dict) -> Generator[str, None, None]:
        """生成流式AI响应"""
        if not self.has_openai:
            # 模拟流式响应
            mock_response = self.generate_mock_analysis(contexts, prompt)
            for i in range(0, len(mock_response), 50):
                chunk = mock_response[i:i+50]
                yield f"data: {json.dumps({'type': 'ai_chunk', 'data': chunk})}\n\n"
            return
        
        try:
            # 构建分析内容
            analysis_content = self.build_analysis_content(contexts, prompt)

            # 配置OpenAI，优先使用传入的配置，然后是默认配置
            api_key = openai_config.get('api_key') or self.default_api_key
            base_url = openai_config.get('base_url') or self.default_base_url
            model = openai_config.get('model') or self.default_model

            print(f"🤖 开始流式AI分析，模型: {model}")

            # 检查openai库版本并使用相应的API
            if hasattr(openai, 'OpenAI'):
                # 新版本 openai >= 1.0
                client = openai.OpenAI(
                    api_key=api_key,
                    base_url=base_url
                )

                response = client.chat.completions.create(
                    model=model,
                    messages=[
                        {"role": "system", "content": "你是一个专业的年报分析师，擅长分析企业协同创新情况。"},
                        {"role": "user", "content": analysis_content}
                    ],
                    max_tokens=2000,
                    temperature=0.7,
                    stream=True
                )

                for chunk in response:
                    if chunk.choices and len(chunk.choices) > 0:
                        delta = chunk.choices[0].delta
                        if delta.content:
                            print(f"📝 AI响应块: {delta.content[:50]}...")
                            chunk_data = f"data: {json.dumps({'type': 'ai_chunk', 'data': delta.content})}\n\n"
                            yield chunk_data
                            # 强制刷新输出缓冲区
                            import sys
                            sys.stdout.flush()
            else:
                # 旧版本 openai < 1.0
                # 临时设置配置
                original_api_key = getattr(openai, 'api_key', None)
                original_api_base = getattr(openai, 'api_base', None)

                openai.api_key = api_key
                if base_url != 'https://api.openai.com/v1':
                    openai.api_base = base_url

                response = openai.ChatCompletion.create(
                    model=model,
                    messages=[
                        {"role": "system", "content": "你是一个专业的年报分析师，擅长分析企业协同创新情况。"},
                        {"role": "user", "content": analysis_content}
                    ],
                    max_tokens=2000,
                    temperature=0.7,
                    stream=True
                )

                for chunk in response:
                    if 'choices' in chunk and len(chunk['choices']) > 0:
                        delta = chunk['choices'][0].get('delta', {})
                        if 'content' in delta and delta['content']:
                            print(f"📝 AI响应块: {delta['content'][:50]}...")
                            chunk_data = f"data: {json.dumps({'type': 'ai_chunk', 'data': delta['content']})}\n\n"
                            yield chunk_data
                            # 强制刷新输出缓冲区
                            import sys
                            sys.stdout.flush()

                # 恢复配置
                if original_api_key:
                    openai.api_key = original_api_key
                if original_api_base:
                    openai.api_base = original_api_base
                
        except Exception as e:
            print(f"❌ 流式AI分析失败: {e}")
            yield f"data: {json.dumps({'type': 'error', 'message': f'AI分析失败: {str(e)}'})}\n\n"
    
    def build_analysis_content(self, contexts: List[Dict], prompt: str) -> str:
        """构建分析内容"""
        content_parts = [
            f"分析要求：{prompt}",
            "",
            "以下是从年报中提取的相关上下文信息：",
            ""
        ]
        
        for i, context in enumerate(contexts, 1):
            content_parts.extend([
                f"【上下文 {i}】",
                f"公司：{context['company_name']} ({context['stock_code']})",
            ])

            # 只有当关联方存在时才显示
            if context.get('related_party'):
                content_parts.append(f"关联方：{context['related_party']}")

            content_parts.extend([
                f"发现的关键词：{', '.join(context['keywords_found']) if context['keywords_found'] else '无'}",
                f"上下文内容：{context['context'][:500]}...",
                ""
            ])
        
        # 检查是否有关联方信息
        has_related_parties = any(ctx.get('related_party') for ctx in contexts)

        if has_related_parties:
            content_parts.extend([
                "请基于以上信息进行分析，重点关注：",
                "1. 各公司与关联方的协同创新情况",
                "2. 创新合作的具体形式和内容",
                "3. 协同创新的效果和价值",
                "4. 行业趋势和发展方向",
                "",
                "请用markdown格式输出分析结果。"
            ])
        else:
            content_parts.extend([
                "请基于以上信息进行分析，重点关注：",
                "1. 各公司在相关关键词领域的发展情况",
                "2. 技术创新和业务发展的具体内容",
                "3. 行业发展趋势和竞争态势",
                "4. 未来发展机遇和挑战",
                "",
                "请用markdown格式输出分析结果。"
            ])
        
        return "\n".join(content_parts)
    
    def generate_mock_analysis(self, contexts: List[Dict], prompt: str) -> str:
        """生成模拟分析结果"""
        companies = list(set(ctx['company_name'] for ctx in contexts))
        parties = list(set(ctx.get('related_party') for ctx in contexts if ctx.get('related_party')))
        keywords = list(set(kw for ctx in contexts for kw in ctx['keywords_found']))

        # 检查是否有关联方信息
        has_related_parties = len(parties) > 0

        if has_related_parties:
            return f"""# 年报协同创新分析报告

## 分析概述
本次分析共涉及 {len(companies)} 家公司，{len(parties)} 个关联方，发现 {len(keywords)} 个创新关键词。

## 主要发现

### 1. 协同创新参与公司
- {', '.join(companies[:5])}{'等' if len(companies) > 5 else ''}

### 2. 主要合作伙伴
- {', '.join(parties[:5])}{'等' if len(parties) > 5 else ''}

### 3. 创新关键词分布
- {', '.join(keywords[:10])}{'等' if len(keywords) > 10 else ''}

## 详细分析

### 协同创新模式
基于年报内容分析，发现以下协同创新模式：

1. **产学研合作**：多家公司与高校、科研院所建立合作关系
2. **技术联盟**：企业间形成技术创新联盟
3. **平台共建**：共同建设创新平台和实验室

### 创新成果
- 技术突破：在关键技术领域取得重要进展
- 产品创新：推出具有市场竞争力的新产品
- 标准制定：参与行业标准的制定工作

## 建议
1. 加强产学研合作深度
2. 扩大技术创新联盟规模
3. 提升协同创新效率

*注：本分析基于年报公开信息，仅供参考。*"""
        else:
            return f"""# 年报关键词分析报告

## 分析概述
本次分析共涉及 {len(companies)} 家公司，发现 {len(keywords)} 个相关关键词。

## 主要发现

### 1. 分析公司
- {', '.join(companies[:5])}{'等' if len(companies) > 5 else ''}

### 2. 关键词分布
- {', '.join(keywords[:10])}{'等' if len(keywords) > 10 else ''}

## 详细分析

### 业务发展情况
基于年报内容分析，发现以下发展特点：

1. **技术创新**：各公司在相关技术领域持续投入研发
2. **市场拓展**：积极开拓新市场和业务领域
3. **产品升级**：不断优化产品结构和服务质量

### 行业趋势
- 技术发展：相关技术不断成熟和应用
- 市场需求：市场对相关产品和服务需求增长
- 竞争格局：行业竞争日趋激烈，差异化发展成为关键

### 发展机遇
- 政策支持：国家政策为行业发展提供有力支撑
- 技术进步：新技术应用带来发展机遇
- 市场扩容：市场规模持续扩大

## 建议
1. 加强技术研发投入
2. 优化产品和服务结构
3. 提升市场竞争力

*注：本分析基于年报公开信息，仅供参考。*"""
